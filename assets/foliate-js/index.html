<!DOCTYPE html>
<html lang="en">
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
<title>Anx Reader</title>
<style>
  html {
    height: 100vh;
  }

  body {
    -webkit-user-select: none;
    user-select: none;
    margin: 0 !important;
    height: 100vh;
  }


  #footnote-dialog {
    /*
    padding: var(safe-area-inset-top) var(safe-area-inset-right) var(safe-area-inset-bottom) var(safe-area-inset-left);
    */
    position: fixed;
    width: 80vw;
    height: 80vh;
    max-width: 400px;
    max-height: 200px;
    min-width: 300px;
    min-height: 200px;
    border-radius: 15px;
    border: 1px solid grey;
    -webkit-user-select: none;
    user-select: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    outline: none;
    z-index: 1000;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
  }

  #footnote-dialog main {
    overflow: auto;
    width: 100%;
    height: 100%;
  }
</style>
<div id="footnote-dialog">
  <main></main>
</div>
<script>
  console.log("AnxUA", navigator.userAgent);
</script>
<script src="./dist/bundle.js" type="module"></script>
<script src="./dist/pdf-legacy.js"></script>
<script>
// Enhanced bookmark functionality for DassoShu Reader
window.checkCurrentPageBookmarkEnhanced = () => {
  if (!reader || !reader.view || !reader.view.lastLocation) return

  const currentCfi = reader.view.lastLocation.cfi
  if (!currentCfi) return

  // Call Flutter to check if bookmark exists at current location
  callFlutter('checkBookmarkEnhanced', {
    cfi: currentCfi,
    fraction: reader.view.lastLocation.fraction || 0
  })
}

// Enhanced bookmark checking function that compares CFI ranges
window.checkBookmarkAtCfi = (bookmarkCfi, currentCfi) => {
  if (!bookmarkCfi || !currentCfi) return false

  try {
    // Simple CFI comparison - if they match or are very close, consider it a match
    const bookmarkBase = bookmarkCfi.split('!')[0] || bookmarkCfi
    const currentBase = currentCfi.split('!')[0] || currentCfi

    return bookmarkBase === currentBase || bookmarkCfi === currentCfi
  } catch (e) {
    console.warn('Error comparing CFIs:', e)
    return bookmarkCfi === currentCfi
  }
}

// Add missing removeBookmarkAtCurrentLocation function
window.removeBookmarkAtCurrentLocation = () => {
  if (!reader || !reader.view || !reader.view.lastLocation) return

  const currentCfi = reader.view.lastLocation.cfi
  if (!currentCfi) return

  // Call Flutter to remove bookmark at current location
  callFlutter('removeBookmark', currentCfi)
}
</script>